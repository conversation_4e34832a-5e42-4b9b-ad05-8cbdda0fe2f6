{"name": "@roshn/seller-dashboard", "private": true, "sideEffects": false, "type": "module", "scripts": {"preinstall": "npx only-allow pnpm", "postinstall": "pnpm exec playwright install", "build": "cross-env NODE_OPTIONS=--max-old-space-size=8192 NODE_ENV=production remix vite:build", "dev:msw": "cross-env VITE_ENABLE_MSW=1 pnpm dev", "dev": "remix vite:dev", "lint": "eslint . --ext .ts,.tsx,.js,.jsx --cache --cache-location .eslintcache", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix --cache --cache-location .eslintcache", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,md,yaml,yml}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,css,md,yaml,yml}\"", "typecheck": "tsc --noEmit", "validate": "cross-env PLAYWRIGHT_HTML_OPEN=never concurrently \"pnpm:test\" \"pnpm:e2e\" \"pnpm:typecheck\" \"pnpm:lint\" \"pnpm:format:check\"", "fix": "pnpm run format && pnpm run lint:fix && pnpm run typecheck", "start": "remix-serve ./build/server/index.js", "test": "vitest run", "test:ui": "vitest --ui", "e2e": "playwright test", "e2e:ci": "playwright test --reporter=list", "e2e:ui": "playwright test --ui", "e2e:report": "playwright show-report", "coverage": "vitest run --coverage", "clean": "rm -rf build .cache .remix .tsbuildinfo .eslintcache node_modules/.cache", "prepare": "husky", "cm": "cz"}, "lint-staged": {"**/*.{ts,tsx,js,jsx,css}": ["pnpm run lint:fix", "pnpm run format"], "**/*.{json,yaml,yml,md}": ["pnpm run format"]}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/css": "^11.13.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@forgerock/javascript-sdk": "^4.3.0", "@hookform/resolvers": "^5.1.1", "@mcansh/http-helmet": "^0.13.0", "@radix-ui/react-icons": "^1.3.2", "@remix-run/node": "^2.16.7", "@remix-run/react": "^2.16.7", "@remix-run/serve": "^2.16.7", "@roshn/forgerock-bridge": "file:fr-package.tgz", "@roshn/shared": "file:shared-package.tgz", "@roshn/ui-kit": "0.0.0-SNAPSHOT-20250717130355", "@sentry/react": "^9.27.0", "@tanstack/react-query": "^5.79.2", "@tiptap/core": "^2.25.0", "@tiptap/extension-character-count": "^2.25.0", "@tiptap/extension-color": "^2.25.0", "@tiptap/extension-link": "^2.25.0", "@tiptap/extension-list-item": "^2.25.0", "@tiptap/extension-placeholder": "^3.0.2", "@tiptap/extension-text-style": "^2.25.0", "@tiptap/extension-underline": "^2.25.0", "@tiptap/pm": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "accept-language-parser": "^1.5.0", "axios": "^1.9.0", "delay": "^6.0.0", "i18next": "^25.2.1", "i18next-chained-backend": "^4.6.2", "i18next-fs-backend": "^2.6.0", "inversify": "^6.0.2", "inversify-react": "^1.2.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "mitt": "^3.0.1", "prettier": "^3.5.3", "ramda": "^0.31.3", "react": "^18.2.0", "react-colorful": "^5.6.1", "react-dom": "^18.2.0", "react-hook-form": "^7.57.0", "react-i18next": "^15.5.2", "react-laag": "^2.0.5", "react-select": "^5.10.1", "reflect-metadata": "^0.2.2", "remix-utils": "^8.7.0", "rollup-plugin-visualizer": "^6.0.1", "typescript-cookie": "^1.0.6", "use-dehydrated-state": "^0.1.0", "vite-imagetools": "^7.1.0", "vite-plugin-cjs-interop": "^2.2.0", "vite-plugin-lqip": "^0.0.5", "zod": "^3.25.61", "zustand": "^5.0.5"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@playwright/test": "^1.53.0", "@remix-run/dev": "^2.16.7", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/accept-language-parser": "^1.5.8", "@types/lodash": "^4.17.20", "@types/node": "^22.15.29", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "@vitejs/plugin-react": "^4.5.1", "@vitest/coverage-v8": "3.2.1", "@vitest/ui": "^3.2.0", "commitizen": "^4.3.1", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "cz-vinyl": "^2.5.4", "eslint": "^8.38.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.0", "msw": "^2.10.2", "remix-flat-routes": "^0.8.5", "resize-observer-polyfill": "^1.5.1", "type-fest": "^4.41.0", "typescript": "^5.1.6", "vite": "^6.0.0", "vite-plugin-devtools-json": "^0.1.0", "vite-plugin-mkcert": "^1.17.8", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^4.2.1", "vitest": "^3.2.0", "zustand": "^4.5.2"}, "engines": {"node": ">=20.0.0"}, "version": "1.0.0", "main": "index.js", "repository": "https://gitlab.com/ROSHN.com/roshn-com-application/frontend/roshn-seller-dashboard-ui.git", "author": "Keshav Sultania <<EMAIL>>", "license": "MIT", "config": {"commitizen": {"path": "cz-vinyl"}}, "msw": {"workerDirectory": ["public"]}}