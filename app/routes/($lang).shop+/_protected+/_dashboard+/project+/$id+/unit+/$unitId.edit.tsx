import { useParams } from "@remix-run/react";
import { useEffect, useState } from "react";
import { useInjection } from "~/hooks/use-di";
import AddProductPage from "~/pages/add-product/add-product";
import { ProductService } from "~/services/product/product";

export default function UnitEdit() {
  const productService = useInjection<ProductService>(ProductService);
  const { unitId } = useParams();
  const [productData, setProductData] = useState<any>(null);
  const getProductDetails = async () => {
    const data = await productService.productDetail(unitId);
    setProductData(data);
  };
  console.log(productData, "Data");
  useEffect(() => {
    getProductDetails();
  }, []);
  return <AddProductPage defaultValues={productData} />;
}
