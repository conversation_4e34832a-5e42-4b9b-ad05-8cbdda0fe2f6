import { css, useTheme } from "@emotion/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate, useParams } from "@remix-run/react";
import { AppTheme, RDSButton, RDSTypography, RDSSwitch } from "@roshn/ui-kit";
import { useState, useMemo } from "react";
import { useForm, Controller } from "react-hook-form";
import { z, ZodTypeAny } from "zod";
import isEmpty from "lodash/isEmpty";

import { Input } from "~/components/form-components/input/input";
import { Select } from "~/components/form-components/select/select";
import { DatePicker } from "~/components/form-components/date-picker/date-picker";
import { TagSelector } from "~/components/form-components/tag-selector/tags-selector";
import { ButtonFileUpload } from "~/components/form-components/file-upload/button-file-upload";
import { TextArea } from "~/components/form-components/text-area/text-area";
import { useAppPath } from "~/hooks/use-app-path";
import { useInjection } from "~/hooks/use-di";
import { ProductService } from "~/services/product/product";
import { AppPaths } from "~/utils/app-paths";
import { InputNumber } from "~/components/form-components/input-number/input-number";
import { Section } from "~/components/sections/sections";
import { formatDateToDDMMYYYY, unitFormSchema } from "~/utils/helper";
import { useStore } from "~/store/store";

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),

  form: css({
    flex: "0 0 70%",
  }),

  sectionLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  sectionWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      backgroundColor: theme.rds.color.background.ui.canvas,
      padding: theme.rds.dimension["300"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  sectionHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
      textTransform: "uppercase",
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  infoHead: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.ui.tertiary,
    }),

  infoDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.emphasis.lg,
      color: theme.rds.color.text.ui.primary,
    }),

  internalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  hideShowUnitWrapper: () =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
    }),

  sectionChildrenWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  sectionDivider: (theme: AppTheme) =>
    css({
      height: "1px",
      backgroundColor: theme.rds.color.border.ui.primary,
    }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      height: "fit-content",
      width: "100%",
    }),

  infoTypoWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  infoSections: (theme: AppTheme) =>
    css({
      flex: "0 0 30%",
      position: "sticky",
      top: theme.rds.dimension["400"],
      alignSelf: "flex-start",
    }),

  actionsLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),

  listingTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      color: theme.rds.color.text.ui.tertiary,
    }),

  withLabelFieldWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      gap: theme?.rds?.dimension[100],
    }),
  fieldLabel: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.md,
      color: theme.rds.color.text.ui.primary,
      display: "flex",
      alignItems: "center",
      gap: theme?.rds?.dimension[50],
    }),
};

const baseTypeMap: Record<string, (attr: any) => ZodTypeAny> = {
  TEXT: () => z.string().min(1, "This field is required"),
  TEXTAREA: () => z.string().min(1, "This field is required"),
  NUMBER: () => z.string().min(1, "Minimum value is 100"),
  COUNTER: () =>
    z.union([
      z.number().min(0, "Value must be 0 or greater"),
      z.string().transform((val) => {
        const num = parseInt(val, 10);
        if (isNaN(num)) return 0;
        return Math.max(0, num);
      }),
    ]),
  MULTI_SELECT: () => z.array(z.string().min(1, "This field is required")),
  DATE: () =>
    z
      .date({
        required_error: "Date is required",
        invalid_type_error: "Invalid date",
      })
      .transform((val) => formatDateToDDMMYYYY(val)),
  BOOLEAN: () => z.any(),
  UPLOAD: () => z.array(z.instanceof(File)).min(1, "At least one document is required"),
  SELECT: (attr) => {
    if (attr.options && attr.options.length > 0) {
      const optionValues = attr.options.map((opt: any) => opt.value || opt);
      return z.enum([...new Set(optionValues)] as [string, ...string[]]);
    }
    return z.string().min(1, "This field is required");
  },
};

const createZodFieldSchema = (attr: any): ZodTypeAny => {
  const generator = baseTypeMap[attr.attribute_type] ?? (() => z.any());
  let schema = generator(attr);

  if (!attr.is_required) {
    schema = schema.optional();
  }

  return schema;
};

const createFormSchema = (formSchemaData: any) => {
  const schemaFields: Record<string, ZodTypeAny> = {};

  formSchemaData.product_attributes.forEach((attr: any) => {
    schemaFields[attr.slug] = createZodFieldSchema(attr);
  });

  return z.object(schemaFields);
};

export default function AddProductPage({
  defaultValues = undefined,
}: {
  defaultValues?: Record<string, any>;
}) {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();
  const productService = useInjection<ProductService>(ProductService);
  const [loading, setLoading] = useState(false);
  const theme = useTheme() as AppTheme;
  const { id } = useParams();

  const dynamicSchema = useMemo(() => {
    return createFormSchema(unitFormSchema);
  }, []);

  const memoizedDefaultValues = useMemo(() => {
    const defaults: Record<string, any> = {};

    const getDefaultValueForType = (attr: any, defaultValuesAttr?: any) => {
      if (defaultValuesAttr?.value !== undefined && defaultValuesAttr?.value !== null) {
        return defaultValuesAttr.value;
      }

      if (defaultValues?.[attr.slug] !== undefined) {
        return defaultValues[attr.slug];
      }

      if (attr.value !== undefined && attr.value !== null) {
        return attr.value;
      }

      switch (attr.attribute_type) {
        case "COUNTER":
          return 0;
        case "NUMBER":
          return "";
        case "BOOLEAN":
          return false;
        case "MULTI_SELECT":
          return [];
        case "UPLOAD":
          return [];
        default:
          return "";
      }
    };

    unitFormSchema.product_attributes.forEach((attr: any) => {
      const defaultValuesAttr = defaultValues?.product_attributes?.find(
        (defaultAttr: any) => defaultAttr.slug === attr.slug,
      );
      defaults[attr.slug] = getDefaultValueForType(attr, defaultValuesAttr);
    });

    return defaults;
  }, [defaultValues]);

  const {
    handleSubmit,
    formState: { isValid, errors, touchedFields },
    control,
    watch,
  } = useForm({
    resolver: zodResolver(dynamicSchema),
    mode: "onChange",
    defaultValues: memoizedDefaultValues,
  });

  const handleInventoryNav = () => {
    navigate(generateAppPath(AppPaths.projectDetail, { id }));
  };

  const handleDiscard = () => {
    navigate(generateAppPath(AppPaths.projectDetail, { id }));
  };

  const toHumanReadable = (label: string) => {
    return label.replace(/([a-z])([A-Z])/g, "$1 $2").replace(/^./, (str) => str.toUpperCase());
  };

  const fieldsError = !isEmpty(errors);
  const fieldsTouched = !isEmpty(touchedFields);

  let statusMessage = "";

  if (fieldsError) {
    statusMessage = "Fix the errors marked in the form before saving.";
  } else if (fieldsTouched) {
    statusMessage = "You have unsaved changes.";
  } else {
    statusMessage = "No changes to be saved.";
  }

  const RenderFieldWithLabel = ({
    name,
    children,
  }: {
    name: string;
    children: React.ReactNode;
  }) => (
    <div css={styles.withLabelFieldWrapper(theme)}>
      <RDSTypography
        css={styles.fieldLabel(theme)}
        fontName={theme?.rds?.typographies?.label.md}
        color={theme?.rds?.color?.text?.ui?.primary}
      >
        {toHumanReadable(name)}
        {<span css={{ color: theme?.rds?.color?.text?.functional?.danger?.tertiary }}>*</span>}
      </RDSTypography>
      {children}
    </div>
  );

  const renderFormField = (attribute: any) => {
    const { id, name, slug, attribute_type, options, is_required } = attribute;
    const key = `${slug}-${id}`;

    switch (attribute_type) {
      case "TEXT":
        const isDescriptionField = name.toLowerCase().includes("description");

        return isDescriptionField ? (
          <TextArea
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            placeholder={`Enter ${name.toLowerCase()}...`}
            rows={4}
          />
        ) : (
          <Input
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            isRequired={is_required}
            placeholder={`Enter ${toHumanReadable(name).toLowerCase()}...`}
          />
        );

      case "NUMBER":
        return (
          <Input
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            type="number"
            isRequired={is_required}
            placeholder={`Enter ${toHumanReadable(name).toLowerCase()}...`}
          />
        );

      case "COUNTER":
        return (
          <RenderFieldWithLabel name={name}>
            <InputNumber key={key} name={slug} control={control} label={toHumanReadable(name)} />
          </RenderFieldWithLabel>
        );

      case "SELECT":
        return (
          <Select
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            isRequired={is_required}
            placeholder={`Select ${toHumanReadable(name).toLowerCase()}...`}
            options={options.map((option: string) => ({
              label: option,
              value: option,
            }))}
          />
        );

      case "MULTI_SELECT":
        return (
          <TagSelector
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            description={`Select multiple ${toHumanReadable(name).toLowerCase()}`}
            options={options.map((option: string) => ({
              label: option,
              value: option.toLowerCase().replace(/\s+/g, "-"),
            }))}
          />
        );

      case "BOOLEAN":
        return (
          <RenderFieldWithLabel name={name}>
            <Controller
              key={key}
              name={slug}
              control={control}
              render={({ field: { value, onChange } }) => (
                <RDSSwitch checked={value || false} onChange={(checked) => onChange(checked)} />
              )}
            />
          </RenderFieldWithLabel>
        );

      case "DATE":
        return (
          <DatePicker
            key={key}
            name={slug}
            control={control}
            startDate={new Date()}
            dateInputProps={{
              helperText: `Select ${name.toLowerCase()}`,
            }}
          />
        );

      case "UPLOAD":
        return (
          <ButtonFileUpload
            key={key}
            name={slug}
            control={control}
            label={`Upload ${name}`}
            accept="image/*"
            multiple={true}
          />
        );

      default:
        return (
          <Input
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            isRequired={is_required}
            placeholder={`Enter ${toHumanReadable(name).toLowerCase()}...`}
          />
        );
    }
  };

  const renderAssetCategoryUpload = (assetCategoryList: any) => {
    const acceptTypes = (allowed_types: []) =>
      allowed_types
        .map((type: string) => {
          if (type === "pdf") return ".pdf";
          if (type === "jpg") return ".jpg,.jpeg";
          if (type === "png") return ".png";
          return `.${type}`;
        })
        .join(",");

    const fileUploadArray = assetCategoryList.map((assetCategory: any, index: number) => ({
      index: index,
      key: `asset-${assetCategory.slug}-${assetCategory.id}`,
      name: assetCategory.slug,
      control: control,
      label: `Upload ${assetCategory.name}`,
      accept: acceptTypes,
      multiple: true,
    }));
    return <ButtonFileUpload {...fileUploadArray[0]} />;
  };

  const transformAndSubmit = async (formData: any) => {
    setLoading(true);

    const updatedSchema = JSON.parse(JSON.stringify(unitFormSchema));

    updatedSchema.product_attributes = updatedSchema.product_attributes.map((attr: any) => ({
      ...attr,
      value: formData[attr.slug] || attr.value || null,
    }));

    formData.custom_attributes = Object.entries(formData).map(([key, value]) => {
      if (key !== "image-gallery") {
        return {
          label: key,
          value: String(value),
        };
      }
      delete formData[key];
      return null;
    }).filter((category) => category !== null && category !== undefined);
    const categoryDetails = useStore().getState().categoryDetails;
    formData.title = `${categoryDetails?.title} - ${formData["unit-code"]}`;
    formData.marketplace_categories = [categoryDetails?.marketplace_category.id];
    formData.marketplace_merchant_category = id;

    await productService.createProduct(formData);
    setLoading(false);
    navigate(generateAppPath(AppPaths.projectDetail, { id }));
  };

  return (
    <div css={styles.wrapper}>
      <RDSButton
        css={styles.button}
        variant="tertiary"
        size="lg"
        text="Back to details page"
        leadIcon="left_arrow"
        onClick={handleInventoryNav}
      />
      <div css={styles.sectionsWrapper}>
        <form css={[styles.form, styles.sectionLayout]} onSubmit={handleSubmit(transformAndSubmit)}>
          {unitFormSchema.product_attributes.length > 0 && (
            <Section heading="PRODUCT ATTRIBUTES">
              {unitFormSchema.product_attributes
                .sort((a, b) => a.order - b.order)
                .map((field) => 
                renderFormField(field)
                )}
            </Section>
          )}

        </form>
        <div css={[styles.sectionLayout, styles.infoSections]}>
          <Section heading="Actions">
            <div css={styles.internalWrapper}>
              <RDSTypography
                css={[
                  styles.actionsHeadingText,
                  fieldsError && { color: theme.rds.color.text.functional.danger.tertiary },
                ]}
              >
                {statusMessage}
              </RDSTypography>
              <RDSButton
                variant="primary"
                loading={loading}
                onClick={handleSubmit(transformAndSubmit)}
                size="lg"
                text="Save Changes"
                disabled={!isValid}
              />
              <RDSButton
                variant="secondary"
                size="lg"
                onClick={handleDiscard}
                text="Discard Changes"
              />
            </div>
          </Section>

          <Section heading="UNIT VISIBILITY" tag={{ label: "Hidden", appearance: "neutral" }}>
            <div css={styles.hideShowUnitWrapper}>
              <RDSTypography fontName={theme?.rds?.typographies?.label?.md}>
                Show to Customers
                <RDSTypography fontName={theme?.rds?.typographies?.label?.sm}>
                  Saved changes update the live unit instantly.
                </RDSTypography>
              </RDSTypography>
              <RDSSwitch checked={false} disabled={!isValid} onChange={() => {}} />
            </div>
          </Section>

          <Section heading="Information">
            <div css={styles.actionsLayout}>
              <div css={styles.infoTypoWrapper}>
                <RDSTypography css={styles.infoHead}>Created on</RDSTypography>
                <RDSTypography css={styles.infoDes}>--</RDSTypography>
              </div>
              <div css={styles.infoTypoWrapper}>
                <RDSTypography css={styles.infoHead}>Created by</RDSTypography>
                <RDSTypography css={styles.infoDes}>--</RDSTypography>
              </div>
              <div css={styles.infoTypoWrapper}>
                <RDSTypography css={styles.infoHead}>Updated on</RDSTypography>
                <RDSTypography css={styles.infoDes}>--</RDSTypography>
              </div>
              <div css={styles.infoTypoWrapper}>
                <RDSTypography css={styles.infoHead}>Updated by</RDSTypography>
                <RDSTypography css={styles.infoDes}>--</RDSTypography>
              </div>
            </div>
          </Section>
        </div>
      </div>
    </div>
  );
}
