import { css, useTheme } from "@emotion/react";
import { useNavigate } from "@remix-run/react";
import {
  RDSTable,
  RDSTagInteractive,
  RDSButton,
  RDSTypography,
  AppTheme,
  RDSSearchInput,
  RDSEmptyState,
  RDSPagination,
  Image,
} from "@roshn/ui-kit";
import { useState } from "react";
import { statusVariantMap, visibilityVariantMap } from "~/constants/project-tag-config";
import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";

import { useAppPath } from "~/hooks/use-app-path";
import { useProjectList } from "~/services/project/hooks/use-project";
import { AppPaths } from "~/utils/app-paths";

type VariantMapType = {
  [key: string]: { variant: string };
};

const tagData = [
  { label: "All", state: "active" },
  { label: "Drafted", state: "default" },
  { label: "In review", state: "default" },
  { label: "Published", state: "default" },
];

const tableData = {
  columns: [
    {
      id: "column1",
      header: "Projects",
      accessor: "column1",
      type: "lead",
    },
    {
      id: "column2",
      header: "Units Sold",
      accessor: "column2",
      type: "text",
    },
    {
      id: "column3",
      header: "Listing Status",
      accessor: "column3",
      type: "tag",
    },
    {
      id: "column4",
      header: "Visibility",
      accessor: "column4",
      type: "tag",
    },
    {
      id: "actions",
      header: "Actions",
      accessor: "id",
      type: "action",
    },
  ],
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      padding: theme.rds.dimension["600"],
      background: theme?.rds?.color?.background?.brand?.secondary?.inverse?.default,
      minHeight: "100vh",
      gap: theme.rds.dimension["200"],
    }),
  emptyStateWrapper: (theme: AppTheme) =>
    css({
      margin: "auto",
      marginTop: "0",
      padding: `${theme?.rds.dimension[500]} 0`,
    }),
  headerContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),
  header: () =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      alignContent: "center",
    }),
  searchInput: (theme: AppTheme) =>
    css({
      gap: theme.rds.dimension["200"],
    }),
  leadImage: () =>
    css({
      width: "5.5rem",
      height: "1.75rem",
    }),
  tagContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      marginTop: theme.rds.dimension["300"],
    }),
};

export default function ProjectsPage() {
  const limit = 10;
  const theme = useTheme() as AppTheme;
  const generatePath = useAppPath();
  const navigate = useNavigate();
  const [activePage, setActivePage] = useState(1);
  const offset = activePage > 0 ? activePage * 10 : 0;
  const { data, isFetching, isError } = useProjectList();
  const results = data?.results || [];
  const totalCount = Math.ceil(data?.count / limit) || 0;

  const handleAddProject = () => {
    navigate(generatePath(AppPaths.addProject));
  };

  const handleProjectDetail = (id: string) => {
    navigate(generatePath(AppPaths.projectDetail, { id }));
  };

  const getVariantByStatus = (variantMap: VariantMapType, status: string): string | undefined => {
    return variantMap[status.toUpperCase()]?.variant;
  };

  const leadIconProject = (src: string) => (
    <Image css={styles.leadImage} src={src} alt="Organization Logo" />
  );

  const createColumnData = () =>
    [...[], ...results].map((projectData: unknown, index: number) => ({
      id: `row-${index}`,
      column1: {
        dataValue: (
          <div css={{ cursor: "pointer" }} onClick={() => handleProjectDetail(projectData?.id)}>
            {projectData?.title}
          </div>
        ),
        leadIcon: leadIconProject(
          projectData?.assets_grouped_by_category["logo"]?.assets[0].url ||
            "https://************.nip.io/roshn_group_logo_with_text_4b8be975b2/roshn_group_logo_with_text_4b8be975b2.svg",
        ),
      },
      column2: {
        dataValue: `${projectData?.analytics?.product_sold_count}/${projectData?.analytics?.product_count}`,
      },
      column3: {
        dataValue: projectData?.status?.approval_status,
        tagType: getVariantByStatus(statusVariantMap, projectData?.status?.approval_status),
      },
      column4: {
        dataValue: projectData?.status?.customer_visibility_status,
        tagType: getVariantByStatus(
          visibilityVariantMap,
          projectData?.status?.customer_visibility_status,
        ),
      },
    }));

  return (
    <div css={styles.wrapper(theme)}>
      <div css={styles.headerContainer(theme)}>
        <div css={styles.header()}>
          <div>
            <RDSTypography fontName={theme?.rds?.typographies?.display?.d5}>Projects</RDSTypography>
          </div>
          <div>
            <RDSButton onClick={handleAddProject} size="lg" text="+ Add project" />
          </div>
        </div>
        <div>
          <RDSSearchInput
            type="text"
            placeholder="Search by name, city, status..."
            css={styles.searchInput(theme)}
          />
          <div css={styles.tagContainer(theme)}>
            {tagData.map((tag) => (
              <RDSTagInteractive
                key={tag.label}
                label={tag.label}
                state={tag.state as "default" | "active"}
              />
            ))}
          </div>
        </div>
      </div>
      {isFetching ? (
        <RoshnContainerLoader />
      ) : !isError ? (
        <RDSTable
          actionText={"View Project"}
          title={tableData.title}
          description={tableData.description}
          columns={tableData.columns}
          data={createColumnData()}
          slotBottom={
            <RDSPagination
              pageCount={totalCount}
              activePage={activePage}
              onPageChange={(setPage) => setActivePage(setPage)}
            />
          }
        />
      ) : (
        <div css={styles.emptyStateWrapper}>
          <RDSEmptyState
            showMedia={false}
            title="You don’t have any listed projects"
            size="sm"
            description="Projects you create will appear here. Add new projects now to fill up your list."
            buttons={[
              {
                text: "ADD YOUR FIRST PROJECT",
                variant: "primary",
                onClick: handleAddProject,
              },
            ]}
          />
        </div>
      )}
    </div>
  );
}
