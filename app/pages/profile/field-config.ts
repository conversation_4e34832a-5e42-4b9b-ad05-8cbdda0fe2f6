export const profileFields = {
  profileDetails: [
    {
      attribute_type: "TEXT",
      name: "nameEn",
      label: "Developer name (English)",
      placeholder: "Example inc...",
      isRequired: true,
      helperText: "This name will be visible in the english version of your public profile page.",
    },
    {
      attribute_type: "TEXT",
      name: "nameAR",
      label: "اسم المطوّر (بالعربية)",
      placeholder: "مثال: مشروع الإبتكار العقاري...",
      isRequired: true,
      helperText: "سيظهر هذا الاسم في النسخة العربية من ملفك التعريفي العام.",
      dir: "rtl",
    },
    {
      attribute_type: "TEXT",
      name: "crn",
      defaultValue: "123456",
      disabled: true,
      label: "Commercial registration number (Fetched from enrollment)",
      isRequired: true,
      helperText: "Your official commercial registration number.",
    },
    {
      attribute_type: "EDITOR",
      name: "descriptionEn",
      label: "Description",
      placeholder:
        "This description will be displayed on your public profile. Use this space to introduce yourself, highlight your development experience, and showcase your key projects. ",
      isRequired: true,
      helperText: "This description will be visible in the english version of your profile page.",
    },
    {
      attribute_type: "EDITOR",
      name: "descriptionAr",
      label: "الوصف",
      placeholder:
        "سيظهر هذا الوصف في ملفك التعريفي العام. يمكنك استخدام هذه المساحة لتعريف نفسك، وإبراز خبراتك في التطوير، وعرض أهم مشاريعك.",
      isRequired: true,
      helperText: "سيظهر هذا الوصف في النسخة العربية من ملفك التعريفي العام.",
      dir: "rtl",
      limit: 50,
    },
  ],
  profileTheme: [
    {
      attribute_type: "UPLOAD_FILE",
      name: "logo",
      label: "Developer logo",
      caption: "JPG or PNG less than 5MB",
      isRequired: true,
    },
    {
      attribute_type: "COLOR_PICKER",
      name: "color",
      label: "Primary brand color",
      caption:
        "This color defines your profile’s theme and will be used in the banner background. Use the color picker or enter a HEX code.",
      isRequired: true,
      defaultValue: "#ffffff",
    },
  ],

  profileContactDetails: [
    {
      attribute_type: "PHONE_NUMBER",
      name: "phoneNumber",
      label: "Phone number",
      placeholder: "Example inc...",
      helperText: "This phone number will be visible on your profile page.",
    },
    {
      attribute_type: "TEXT",
      name: "email",
      label: "Email address",
      placeholder: "<EMAIL>",
      helperText: "This email address will be visible on your profile page.",
    },
  ],
};
