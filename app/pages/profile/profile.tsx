import { css } from "@emotion/react";
import { AppTheme, R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, RDSTypography } from "@roshn/ui-kit";

import { Section } from "~/components/section/section";
import { createSvg } from "~/components/svgs";
import ProfileForm from "~/features/profile/profile-form";
import { useDynamicForm } from "~/hooks/use-dynamic-hook";
import SectionLayout from "~/layouts/SectionLayout";

import { profileFields } from "./field-config";

export const Eye = createSvg(() => import("~/assets/icons/eye.svg"));

const styles = {
  itemsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      color: theme.rds.color.text.ui.tertiary,
    }),
};

export default function Profile() {
  const {
    control,
    formState: { isValid },
    reset,
  } = useDynamicForm([
    ...profileFields.profileTheme,
    ...profileFields.profileDetails,
    ...profileFields.profileContactDetails,
  ]);

  const primarySection = () => {
    return <ProfileForm control={control} />;
  };

  const secondarySection = () => {
    return (
      <Section heading="Actions">
        <div css={styles.itemsWrapper}>
          <RDSTypography css={styles.actionsHeadingText}>
            Complete all required fields to save changes
          </RDSTypography>
          <RDSButton
            variant="primary"
            size="md"
            text="SAVE CHANGES"
            disabled={!isValid}
            onClick={() => {}}
          />
          <RDSButton
            variant="secondary"
            size="md"
            text="DISCARD CHANGES"
            onClick={() => {
              reset({});
            }}
          />
          <RDSButton
            variant="tertiary"
            leadIcon={
              <RDSAssetWrapper>
                <Eye />
              </RDSAssetWrapper>
            }
            size="md"
            text="PREVIEW PUBLIC PROFILE"
            onClick={() => {}}
          />
        </div>
      </Section>
    );
  };

  return <SectionLayout primarySection={primarySection} secondarySection={secondarySection} />;
}
