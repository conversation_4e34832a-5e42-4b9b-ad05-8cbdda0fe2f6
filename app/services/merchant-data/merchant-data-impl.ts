import { inject, injectable } from "inversify";

import { RestHelper } from "../rest-helper";
import { MerchantDataInterface } from "./merchant-data";
import { useStore } from "~/store/store";

@injectable()
export class MerchantDataImpl implements MerchantDataInterface {
  constructor(
    @inject(RestHelper)
    private readonly restHelper: RestHelper,
  ) {}

  async getMerchantData(): Promise<unknown> {
    const merchantData = await this.restHelper.get(`/api/v1/merchant/data/`);
    useStore().getState().setMerchantData(merchantData);
    return merchantData;
  }

  getMarketPlace(): string {
    return useStore().getState().merchantData?.marketplace?.title ?? "brokerage";
  }

  getTheme(): unknown {
    return useStore().getState().merchantData?.global_styles ?? {};
  }
}
