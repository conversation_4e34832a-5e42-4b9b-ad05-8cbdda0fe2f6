import { useQuery } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { ProjectService } from "../project";

export function useProjectList() {
  const projectService = useInjection<ProjectService>(ProjectService);

  return useQuery({
    queryKey: [QueryKey.PROJECT_LIST],
    queryFn: () => projectService.getProjectList(),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

export function useProjectDetail(args: string) {
  const projectService = useInjection<ProjectService>(ProjectService);

  return useQuery({
    queryKey: [QueryKey.PROJECT_DETAIL],
    queryFn: async () => projectService.getProjectDetail(args),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
