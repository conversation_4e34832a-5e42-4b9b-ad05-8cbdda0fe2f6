import { inject, injectable } from "inversify";
import { ProjectServiceInterface } from "./project";

import { RestHelper } from "~/services/rest-helper";
import { MerchantDataService } from "../merchant-data/merchant-data";
import { useStore } from "~/store/store";

@injectable()
export class ProjectServiceImpl implements ProjectServiceInterface {
  constructor(
    @inject(RestHelper)
    private readonly restHelper: RestHelper,
    @inject(MerchantDataService)
    private readonly merchantDataService: MerchantDataService,
  ) {}

  getProjectList: () => Promise<any> = () => {
    const marketPlaceSlug = this.merchantDataService.getMarketPlace();
    return this.restHelper.get(`/api/v1/marketplaces/${marketPlaceSlug}/merchants/categories/?include_details=true`);
  }

  getProjectDetail: (id: string) => Promise<any> = async (id: string) => {
    const marketPlaceSlug = this.merchantDataService.getMarketPlace();
    const data = await this.restHelper.get(`/api/v1/marketplaces/${marketPlaceSlug}/merchants/categories/${id}`);
    useStore().getState().setCategoryDetails(data);
    return data;
  }

  deleteProject: (id: string) => Promise<any> = (id: string) => {
    const marketPlaceSlug = this.merchantDataService.getMarketPlace();
    return this.restHelper.delete(`/api/v1/marketplaces/${marketPlaceSlug}/merchants/categories/${id}`);
  }
}
