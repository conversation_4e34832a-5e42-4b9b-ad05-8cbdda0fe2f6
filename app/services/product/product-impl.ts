import { inject, injectable } from "inversify";

import { RestHelper } from "~/services/rest-helper";

import { BulkImportResponse, GetProductListResponse, ProductServiceInterface } from "./product";

@injectable()
export class ProductImpl implements ProductServiceInterface {
  constructor(
    @inject(RestHelper)
    private readonly restHelper: RestHelper,
  ) {}

  getProductList: (args: {
    /*
      @params: 
      marketplace_product_status: string;
      search: string;
    */
    marketplace_product_status: string;
  }) => Promise<GetProductListResponse> = (args: { marketplace_product_status: string }) => {
    return this.restHelper.get("/api/v1.2/ecommerce/products/", {
      params: {
        limit: 20,
        offset: 0,
        ordering: "-marketplace_product__date_submitted",
        ...args,
      },
    });
  };

  createProduct: (args: any) => Promise<GetProductListResponse> = (args: any) => {
    return this.restHelper.post("/api/v1.2/ecommerce/products/", {
      data: args,
    });
  };

  getInterestList: () => Promise<GetProductListResponse> = () => {
    return this.restHelper.get("/api/v1.2/ecommerce/orders/", {
      params: {
        limit: 20,
        show_archived_orders: false,
        refetchNumber: 0,
        ordering: "-created_date",
        date: "",
      },
    });
  };

  bulkUploadProduct: (args: File) => Promise<BulkImportResponse> = async (args: File) => {
    const formData = new FormData();
    formData.append("file", args);
    return await this.restHelper.post("/api/v1/ecommerce/products/bulk-import/", {
      data: formData,
    });
  };
  productDetail: (id: string) => Promise<unknown> = async (id: string) => {
    return await this.restHelper.get(`/api/v1.2/ecommerce/products/${id}/`);
  };
}
