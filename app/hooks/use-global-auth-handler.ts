import { useNavigate, useLocation, createSearchParams } from "@remix-run/react";
import { useEffect } from "react";

import { useAppPath } from "~/hooks/use-app-path";
import { useInjection } from "~/hooks/use-di";
import { HttpClientFactory } from "~/services/http-client-factory";
import { useStore } from "~/store/store";
import { AppPaths } from "~/utils/app-paths";

export function useGlobalAuthHandler() {
  const navigate = useNavigate();
  const location = useLocation();
  const generateAppPath = useAppPath();
  const httpClientFactory = useInjection<HttpClientFactory>(HttpClientFactory);
  const setSignedIn = useStore().getState().setSignedIn;

  useEffect(() => {
    const handleUnauthenticated = () => {
      setSignedIn(false);

      const search = createSearchParams({
        redirect: location.pathname + location.search,
      }).toString();

      const loginPath = generateAppPath(AppPaths.login);

      navigate(
        {
          pathname: loginPath,
          search,
        },
        {
          replace: true,
        },
      );
    };

    httpClientFactory.on("unauthenticated", handleUnauthenticated);

    return () => {
      httpClientFactory.off("unauthenticated", handleUnauthenticated);
    };
  }, [navigate, location, generateAppPath, httpClientFactory, setSignedIn]);
}

export function useRedirectIfAuthenticated() {
  const isSignedIn = useStore().getState().signedIn;
  const navigate = useNavigate();
  const generateAppPath = useAppPath();

  useEffect(() => {
    if (isSignedIn) {
      navigate(generateAppPath(AppPaths.dashboard), { replace: true });
    }
  }, [isSignedIn, navigate, generateAppPath]);
}
