import { useQuery } from "@tanstack/react-query";

import { MerchantDataService } from "~/services/merchant-data/merchant-data";

import { QueryKey } from "../context/reactQueryProvider";

import { useInjection } from "./use-di";

export function useMerchantData() {
  const merchantData = useInjection<MerchantDataService>(MerchantDataService);
  return useQuery({
    queryKey: [QueryKey.MERCHANT_DATA],
    queryFn: () => merchantData.getMerchantData(),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
    enabled: false,
  });
}
