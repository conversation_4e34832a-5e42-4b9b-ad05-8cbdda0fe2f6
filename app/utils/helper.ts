export const formatCurrency = (
  value: number | string,
  locale: string = "en-US",
  currency: string = "USD",
): string => {
  const number = typeof value === "string" ? parseFloat(value) : value;

  if (isNaN(number)) return "";

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(number);
};

export const formatDateToDDMMYYYY = (date: Date): string => {
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
};

export const unitFormSchema = {
  id: 1,
  name_en: "Off-plan Properties",
  name_ar: "وحدات عقارية على الخارطة",
  slug: "off-plan-properties",
  template_id: "off_plan_properties_v1",
  version: 1,
  description_en:
    "List your under-construction units. Share project details and floor plans to attract buyers before completion.",
  description_ar:
    "أضف وحداتك العقارية تحت التطوير. شارك تفاصيل المشروع والمخططات لجذب المشترين قبل اكتمال البناء.",
  is_active: true,
  order: 0,
  marketplace: 5,
  marketplace_merchant: null,
  title: "Off-plan Properties",
  product_attributes: [
    {
      id: 33,
      name: "unit code",
      slug: "unit-code",
      attribute_type: "TEXT",
      options: [],
      is_required: true,
      order: 0,
      scope: "PRODUCT",
      section: "unit-details",
      locale: "en",
      label: "Unit code / number",
      placeholder: "Enter unit code or number...",
      helper_text: "Unique identifier visible on the project page.",
      custom_attribute_field_key: "unit_code",
      option_meta: [],
    },
    {
      id: 34,
      name: "property type",
      slug: "property-type",
      attribute_type: "SELECT",
      options: ["apartment", "villa", "townhouse"],
      is_required: true,
      order: 1,
      scope: "PRODUCT",
      section: "unit-details",
      locale: "en",
      label: "Select property type...",
      placeholder: "Select property type...",
      helper_text: "Specify the type of property (e.g., villa, townhouse).",
      custom_attribute_field_key: "property_type",
      option_meta: [],
    },
    {
      id: 35,
      name: "bedrooms",
      slug: "bedrooms",
      attribute_type: "COUNTER",
      options: [],
      is_required: false,
      order: 2,
      scope: "PRODUCT",
      section: "unit-details",
      locale: "en",
      label: "Bedrooms",
      placeholder: "0",
      helper_text: "Number of bedrooms in the unit.",
      custom_attribute_field_key: "bedrooms",
      option_meta: [],
    },
    {
      id: 36,
      name: "bathrooms",
      slug: "bathrooms",
      attribute_type: "COUNTER",
      options: [],
      is_required: true,
      order: 3,
      scope: "PRODUCT",
      section: "unit-details",
      locale: "en",
      label: "Bathrooms",
      placeholder: "0",
      helper_text: "Number of bathrooms in the unit.",
      custom_attribute_field_key: "bathrooms",
      option_meta: [],
    },
    {
      id: 37,
      name: "image-gallery",
      slug: "image-gallery",
      attribute_type: "UPLOAD",
      options: [],
      is_required: true,
      order: 4,
      scope: "PRODUCT",
      section: "media",
      locale: "en",
      label: "image-gallery",
      placeholder: "Browse or drop files",
      helper_text: "Select up to 10 images. Supports JPG, PNG or WEBP less than 5MB.",
      custom_attribute_field_key: "image_gallery",
      option_meta: [],
    },
    {
      id: 38,
      name: "gross floor area sqm",
      slug: "gross-floor-area-sqm",
      attribute_type: "NUMBER",
      options: [],
      is_required: true,
      order: 5,
      scope: "PRODUCT",
      section: "area-details",
      locale: "en",
      label: "Gross floor area (sqm)",
      placeholder: "Enter gross floor area...",
      helper_text: "Total gross floor area in square meters.",
      custom_attribute_field_key: "gross_floor_area_sqm",
      option_meta: [],
    },
    {
      id: 39,
      name: "plot area sqm",
      slug: "plot-area-sqm",
      attribute_type: "NUMBER",
      options: [],
      is_required: true,
      order: 6,
      scope: "PRODUCT",
      section: "area-details",
      locale: "en",
      label: "Plot area (sqm)",
      placeholder: "Enter plot area...",
      helper_text: "Size of the plot/land in square meters.",
      custom_attribute_field_key: "plot_area_sqm",
      option_meta: [],
    },
    {
      id: 40,
      name: "built up area sqm",
      slug: "built-up-area-sqm",
      attribute_type: "NUMBER",
      options: [],
      is_required: false,
      order: 7,
      scope: "PRODUCT",
      section: "area-details",
      locale: "en",
      label: "Built-up area (sqm)",
      placeholder: "Enter built-up area...",
      helper_text: "Built-up area in square meters.",
      custom_attribute_field_key: "built_up_area_sqm",
      option_meta: [],
    },
    {
      id: 41,
      name: "orientation",
      slug: "orientation",
      attribute_type: "SELECT",
      options: ["north", "west", "east", "south"],
      is_required: true,
      order: 8,
      scope: "PRODUCT",
      section: "area-details",
      locale: "en",
      label: "Unit orientation",
      placeholder: "Select unit orientation...",
      helper_text: "Direction the unit faces (e.g., North, South).",
      custom_attribute_field_key: "orientation",
      option_meta: [],
    },
    {
      id: 42,
      name: "additional rooms",
      slug: "additional-rooms",
      attribute_type: "MULTI_SELECT",
      options: [
        "driver_room",
        "Laundry room",
        "maid_room",
        "majlis",
        "powder_room",
        "storage_area",
      ],
      is_required: false,
      order: 9,
      scope: "PRODUCT",
      section: "additional-details",
      locale: "en",
      label: "Additional rooms",
      placeholder: null,
      helper_text: "Select available rooms found in the unit.",
      custom_attribute_field_key: "additional_rooms",
      option_meta: [],
    },
    {
      id: 43,
      name: "unit features",
      slug: "unit-features",
      attribute_type: "MULTI_SELECT",
      options: [
        "accessibility",
        "ac_installed",
        "balcony",
        "broadband_internet",
        "cabinets",
        "cctv_security",
        "elevator",
        "furnished",
        "garden",
        "kitchen_installed",
        "parking",
        "roof",
        "swimming_pool",
        "terrace",
      ],
      is_required: false,
      order: 10,
      scope: "PRODUCT",
      section: "additional-details",
      locale: "en",
      label: "Unit features",
      placeholder: null,
      helper_text: "Select available features and amenities for the unit.",
      custom_attribute_field_key: "unit_features",
      option_meta: [],
    },
    {
      id: 44,
      name: "price",
      slug: "price",
      attribute_type: "NUMBER",
      options: [],
      is_required: true,
      order: 11,
      scope: "PRODUCT",
      section: "price",
      locale: "en",
      label: "Price",
      placeholder: "Enter price...",
      helper_text: "Full unit price.",
      custom_attribute_field_key: "price",
      option_meta: [],
    },
  ],
  category_attributes: [
    {
      id: 19,
      name: "name",
      slug: "name",
      attribute_type: "TEXT",
      options: [],
      is_required: true,
      order: 0,
      scope: "CATEGORY",
      section: "project-details",
      locale: "en",
      label: "Project name (English)",
      placeholder: "Example inc...",
      helper_text: "This name will be visible in the english version of your project page.",
      custom_attribute_field_key: "name_en",
      option_meta: [],
    },
    {
      id: 19,
      name: "name",
      slug: "name",
      attribute_type: "TEXT",
      options: [],
      is_required: true,
      order: 0,
      scope: "CATEGORY",
      section: "project-details",
      locale: "ar",
      label: "اسم المشروع (بالعربية)",
      placeholder: "مثال: مشروع الإبتكار العقاري...",
      helper_text: "سيظهر هذا الاسم في النسخة العربية من صفحة مشروعك.",
      custom_attribute_field_key: "name_ar",
      option_meta: [],
    },
    {
      id: 21,
      name: "logo",
      slug: "logo",
      attribute_type: "UPLOAD",
      options: [],
      is_required: true,
      order: 1,
      scope: "CATEGORY",
      section: "project-details",
      locale: "en",
      label: "Project logo",
      placeholder: "Browse or drop a file",
      helper_text: "JPG or PNG less than 5MB",
      custom_attribute_field_key: "logo",
      option_meta: [],
    },
    {
      id: 20,
      name: "city",
      slug: "city",
      attribute_type: "TEXT",
      options: [],
      is_required: true,
      order: 2,
      scope: "CATEGORY",
      section: "project-details",
      locale: "en",
      label: "City",
      placeholder: "Select a city...",
      helper_text: "Specify the city where the project is located.",
      custom_attribute_field_key: "city",
      option_meta: [],
    },
    {
      id: 22,
      name: "rega license number",
      slug: "rega-license-number",
      attribute_type: "NUMBER",
      options: [],
      is_required: true,
      order: 3,
      scope: "CATEGORY",
      section: "project-details",
      locale: "en",
      label: "REGA license number",
      placeholder: "123...",
      helper_text: "Your official project registration number.",
      custom_attribute_field_key: "rega_license_number",
      option_meta: [],
    },
    {
      id: 23,
      name: "handover date",
      slug: "handover-date",
      attribute_type: "DATE",
      options: [],
      is_required: true,
      order: 4,
      scope: "CATEGORY",
      section: "project-details",
      locale: "en",
      label: "Handover date",
      placeholder: "Select a date...",
      helper_text: "The date when the project will be delivered to the client.",
      custom_attribute_field_key: "handover_date",
      option_meta: [],
    },
    {
      id: 24,
      name: "description",
      slug: "description",
      attribute_type: "LARGE_TEXT",
      options: [],
      is_required: true,
      order: 5,
      scope: "CATEGORY",
      section: "project-details",
      locale: "en",
      label: "Description",
      placeholder:
        "Tell us more about your project (max 500 characters). Use formatting to highlight features.",
      helper_text: "This description will be visible in the english version of your project page.",
      custom_attribute_field_key: "description",
      option_meta: [],
    },
    {
      id: 25,
      name: "nearby amenities",
      slug: "nearby-amenities",
      attribute_type: "TEXT",
      options: [
        "dining_entertainment",
        "health_centre",
        "kindergarten",
        "mosque",
        "public_park",
        "retail_centres",
        "school",
        "sports_ground",
      ],
      is_required: true,
      order: 6,
      scope: "CATEGORY",
      section: "additional-details",
      locale: "en",
      label: "Nearby amenities",
      placeholder: null,
      helper_text: "Select available amenities in the community.",
      custom_attribute_field_key: "nearby_amenities",
      option_meta: [],
    },
    {
      id: 32,
      name: "project documents",
      slug: "project-documents",
      attribute_type: "UPLOAD",
      options: [],
      is_required: false,
      order: 7,
      scope: "CATEGORY",
      section: "attachments",
      locale: "en",
      label: "Project documents",
      placeholder:
        "Upload files to share important project details — they will be organized by category for easy access.",
      helper_text: "Examples: brochures, masterplans, floor plans, etc.",
      custom_attribute_field_key: "project_documents",
      option_meta: [],
    },
    {
      id: 29,
      name: "down payment",
      slug: "down-payment",
      attribute_type: "NUMBER",
      options: [],
      is_required: true,
      order: 7,
      scope: "CATEGORY",
      section: "payment-plan-payment-schedule",
      locale: "en",
      label: "Down payment",
      placeholder: "33.33",
      helper_text: "",
      custom_attribute_field_key: "down_payment",
      option_meta: [],
    },
    {
      id: 26,
      name: "reservation fee refundable",
      slug: "reservation-fee-refundable",
      attribute_type: "BOOLEAN",
      options: [],
      is_required: true,
      order: 8,
      scope: "CATEGORY",
      section: "payment-plan-reservation",
      locale: "en",
      label: "Refundable?",
      placeholder: null,
      helper_text: "",
      custom_attribute_field_key: "reservation_fee_refundable",
      option_meta: [],
    },
    {
      id: 28,
      name: "reservation fee ammount",
      slug: "reservation-fee-ammount",
      attribute_type: "NUMBER",
      options: [],
      is_required: true,
      order: 8,
      scope: "CATEGORY",
      section: "payment-plan-reservation",
      locale: "en",
      label: "Reservation fee amount (SAR)",
      placeholder: "Enter price...",
      helper_text: "Enter the full amount to be paid for the reservation.",
      custom_attribute_field_key: "reservation_fee_ammount",
      option_meta: [],
    },
    {
      id: 27,
      name: "reservation fee type",
      slug: "reservation-fee-type",
      attribute_type: "SELECT",
      options: ["fixed", "percentage"],
      is_required: true,
      order: 9,
      scope: "CATEGORY",
      section: "payment-plan-reservation",
      locale: "en",
      label: "Fee Type",
      placeholder: null,
      helper_text: "",
      custom_attribute_field_key: "reservation_fee_type",
      option_meta: [],
    },
    {
      id: 30,
      name: "during construction",
      slug: "during-construction",
      attribute_type: "TEXT",
      options: [],
      is_required: true,
      order: 12,
      scope: "CATEGORY",
      section: "payment-plan-payment-schedule",
      locale: "en",
      label: "During construction",
      placeholder: "33.33",
      helper_text: "",
      custom_attribute_field_key: "during_construction",
      option_meta: [],
    },
    {
      id: 31,
      name: "on handover",
      slug: "on-handover",
      attribute_type: "NUMBER",
      options: [],
      is_required: true,
      order: 13,
      scope: "CATEGORY",
      section: "payment-plan-payment-schedule",
      locale: "en",
      label: "On handover",
      placeholder: "33.33",
      helper_text: "",
      custom_attribute_field_key: "on_handover",
      option_meta: [],
    },
  ],
  attributes_count: 26,
  product_attributes_count: 12,
  category_attributes_count: 14,
  categories_using_template: [
    {
      id: 1,
      title: "Ewan Sedra",
      slug: "ewan-sedra",
    },
    {
      id: 2,
      title: "Ewan Sedra",
      slug: "ewan-sedra-2",
    },
    {
      id: 3,
      title: "Ewan Warefa",
      slug: "ewan-warefa",
    },
    {
      id: 12,
      title: "My Merchant Category",
      slug: "my-merchant-category-2",
    },
    {
      id: 13,
      title: "My Merchant Category",
      slug: "my-merchant-category-3",
    },
  ],
  created_date: "2025-07-18T10:37:52.044673Z",
  updated_date: "2025-07-20T11:48:59.792903Z",
  marketplace_asset_categories: [
    {
      id: 1,
      name: "Brochures",
      slug: "brochures",
      description: "Brochure for the project or community.",
      allowed_file_types: ["pdf", "jpg", "png", "webp"],
      max_file_size: 5,
      order: 0,
      is_active: true,
      assets_count: 0,
    },
    {
      id: 2,
      name: "Floor Plans",
      slug: "floor-plans",
      description: "Floor Plans for the project or community units.",
      allowed_file_types: ["pdf", "jpg", "png", "webp"],
      max_file_size: 5,
      order: 0,
      is_active: true,
      assets_count: 1,
    },
    {
      id: 4,
      name: "Logo",
      slug: "logo",
      description: "Project or community main logo",
      allowed_file_types: ["jpg", "png", "webp"],
      max_file_size: 5,
      order: 0,
      is_active: true,
      assets_count: 0,
    },
    {
      id: 3,
      name: "Masterplan",
      slug: "masterplan",
      description: "Masterplan for the project or community.",
      allowed_file_types: ["pdf", "jpg", "png", "webp"],
      max_file_size: 5,
      order: 0,
      is_active: true,
      assets_count: 0,
    },
  ],
};
