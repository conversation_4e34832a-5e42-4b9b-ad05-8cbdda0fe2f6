import { getDIContainer } from "~/hooks/use-di";
import { PersistedStore, StoreFactory } from "~/services/store-factory";

import { AuthSlice, MerchantDataSlice, ProfileSlice, createAuthSlice, createProfileSlice } from "./slices";
import { createMerchantDataSlice } from "./slices/merchant-data-slice";

let storeInstance: PersistedStore<AuthSlice & ProfileSlice & MerchantDataSlice> | null = null;

export function useStore(): PersistedStore<AuthSlice & ProfileSlice & MerchantDataSlice> {
  if (!storeInstance) {
    const storeFactory = getDIContainer().get<StoreFactory>(StoreFactory);

    storeInstance = storeFactory.createStore<AuthSlice & ProfileSlice & MerchantDataSlice>(
      (...a) => ({
        ...createAuthSlice(...a),
        ...createProfileSlice(...a),
        ...createMerchantDataSlice(...a),
      }),
      {
        persist: {
          name: "global-store",
          version: 0,
        },
      },
    );
  }

  return storeInstance;
}
