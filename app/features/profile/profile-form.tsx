import { Control } from "react-hook-form";

import { FieldRenderer } from "~/components/field-renderer/field-renderer";
import { Section } from "~/components/section/section";
import { profileFields } from "~/pages/profile/field-config";

export default function ProfileForm({ control }: { control: Control }) {
  return (
    <>
      <Section heading="PROFILE THEME">
        {profileFields.profileTheme.map((rest) => (
          <FieldRenderer key={rest.name} {...rest} control={control} />
        ))}
      </Section>
      <Section heading="PROFILE DETAILS">
        {profileFields.profileDetails.map((rest) => (
          <FieldRenderer key={rest.name} {...rest} control={control} />
        ))}
      </Section>
      <Section heading="PUBLIC CONTACT DETAILS">
        {profileFields.profileContactDetails.map((rest) => (
          <FieldRenderer key={rest.name} {...rest} control={control} />
        ))}
      </Section>
    </>
  );
}
