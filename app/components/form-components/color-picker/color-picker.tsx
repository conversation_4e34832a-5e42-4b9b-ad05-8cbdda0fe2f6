import { css } from "@emotion/react";
import { AppTheme, RDSTextInput, RDSTypography } from "@roshn/ui-kit";
import { HexColorPicker } from "react-colorful";
import { Controller, Control, FieldValues, Path } from "react-hook-form";

const hexToRgb = (hex: string) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) return { r: 0, g: 0, b: 0 };
  return {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16),
  };
};

const rgbToHex = (r: number, g: number, b: number) => {
  return `#${[r, g, b]
    .map((x) => {
      const hex = x.toString(16);
      return hex.length === 1 ? `0${hex}` : hex;
    })
    .join("")}`;
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      alignItems: "flex-start",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],

      "& .react-colorful": {
        width: "50%",
        height: "auto",
      },

      "& .react-colorful__saturation": {
        borderRadius: "8px",
      },

      "& .react-colorful__pointer": {
        height: "32px",
        width: "32px",
        border: `8px solid ${theme.rds.color.base.white}`,
      },

      "& .react-colorful__last-control ": {
        height: "12px",
        borderRadius: "8px",
        marginBlockStart: "24px",
        marginBlockEnd: "14px",
      },
    }),

  pickerWrapper: css({
    display: "flex",
    width: "100%",
    gap: "24px",
  }),

  inputGroup: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      width: "50%",
      gap: theme.rds.dimension["200"],
    }),

  label: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.md,
      color: theme.rds.color.text.ui.primary,
    }),

  caption: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.sm,
      color: theme.rds.color.text.ui.tertiary,
    }),

  required: (theme: AppTheme) =>
    css({
      color: theme.rds.color.text.functional.danger.tertiary,
    }),

  header: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),
};

type ColorPickerProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  caption?: string;
  isRequired?: boolean;
  defaultValue?: string;
};

export default function ColorPicker<T extends FieldValues>({
  name,
  control,
  label,
  isRequired,
  caption,
  defaultValue = "#ffffff",
}: ColorPickerProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => {
        const { value, onChange } = field;
        const rgb = hexToRgb(value);

        const handleRgbChange = (key: "r" | "g" | "b", val: number) => {
          const updated = { ...rgb, [key]: val };
          const hex = rgbToHex(updated.r, updated.g, updated.b);
          onChange(hex);
        };

        return (
          <div css={styles.wrapper}>
            <div css={styles.header}>
              {label && (
                <RDSTypography css={styles.label}>
                  {label}
                  {isRequired && <span css={styles.required}>*</span>}
                </RDSTypography>
              )}
              {caption && <RDSTypography css={styles.caption}>{caption}</RDSTypography>}
            </div>
            <div css={styles.pickerWrapper}>
              <HexColorPicker defaultValue={defaultValue} color={value} onChange={onChange} />
              <div css={styles.inputGroup}>
                <RDSTextInput
                  value={value}
                  onChange={(e) => onChange(e.target.value)}
                  label="Hex"
                />

                <RDSTextInput
                  type="number"
                  value={rgb.r}
                  onChange={(e) => handleRgbChange("r", +e.target.value)}
                  min={0}
                  max={255}
                  label="R"
                />

                <RDSTextInput
                  type="number"
                  value={rgb.g}
                  onChange={(e) => handleRgbChange("g", +e.target.value)}
                  min={0}
                  max={255}
                  label="G"
                />

                <RDSTextInput
                  type="number"
                  value={rgb.b}
                  onChange={(e) => handleRgbChange("b", +e.target.value)}
                  min={0}
                  max={255}
                  label="B"
                />
              </div>
            </div>
          </div>
        );
      }}
    />
  );
}
