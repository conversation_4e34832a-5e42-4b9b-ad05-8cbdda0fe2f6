import { AppTheme, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>on, RDSStepperProps, RDSTextInput } from "@roshn/ui-kit";
import { Controller, Control, FieldValues, Path } from "react-hook-form";
import { useEffect, useRef } from "react";
import { MinusIcon, PlusIcon } from "@radix-ui/react-icons";
import { css } from "@emotion/react";

type ControlledStepperProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  type?: string;
} & Omit<RDSStepperProps, "name" | "value" | "onChange" | "onBlur">;

const styles = {
  inputField: (theme: AppTheme) =>
    css({
      width: theme.rds.dimension["700"],
      height: theme.rds.dimension["500"],
      textAlign: "center",
      border: `${theme.rds.border.borderWidth[100]} solid ${theme.rds.color.border.ui.primary}`,
      color: theme.rds.color.text.ui.primary,
      "&::placeholder": {
        color: theme.rds.color.text.ui.tertiary,
      },
    }),
};

export function InputNumber<T extends FieldValues>({
  name,
  control,
  label,
  helperText,
  ...rest
}: ControlledStepperProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      const buttons = containerRef.current.querySelectorAll("button");
      buttons.forEach((button) => {
        if (!button.getAttribute("type")) {
          button.setAttribute("type", "button");
        }
      });
    }
  });

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => {
        return (
          <div ref={containerRef}>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <RDSIconButton
                icon={<MinusIcon />}
                variant="secondary"
                onClick={() => {
                  const currentValue = Number(field.value) || 0;
                  const newValue = Math.max(0, currentValue - 1);
                  field.onChange(newValue);
                }}
              />
              <input
                css={styles.inputField}
                value={field.value || 0}
                onChange={(e) => {
                  const newValue = parseInt(e.target.value, 10) || 0;
                  field.onChange(newValue);
                }}
                onBlur={field.onBlur}
                min="0"
              />
              <RDSIconButton
                icon={<PlusIcon />}
                onClick={() => {
                  const currentValue = Number(field.value) || 0;
                  const newValue = currentValue + 1;
                  field.onChange(newValue);
                }}
              />
            </div>
            {fieldState.error && (
              <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
                {fieldState.error.message}
              </div>
            )}
          </div>
        );
      }}
    />
  );
}
