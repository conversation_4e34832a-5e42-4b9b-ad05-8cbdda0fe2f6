import { Control } from "react-hook-form";

import { fieldMap } from "./field-map";

type FieldType = keyof typeof fieldMap;

type FieldSchema = {
  // type: FieldType;
  name: string;
  control: Control;
  label?: string;
  placeholder?: string;
  options?: { label: string; value: string }[];
  attribute_type: FieldType;
  [key: string]: any;
};

export const FieldRenderer = (props: FieldSchema) => {
  const Component = fieldMap[props?.attribute_type];

  if (!Component) {
    console.warn(`No component found for field type: ${props.attribute_type}`);
    return null;
  }

  return <Component {...(props as any)} />;
};
