import { RDSUploadFile } from "@roshn/ui-kit";

import ColorPicker from "../form-components/color-picker/color-picker";
import { DatePicker } from "../form-components/date-picker/date-picker";
import { RichTextEditor } from "../form-components/editor/editor";
import { ButtonFileUpload } from "../form-components/file-upload/button-file-upload";
import { UploadFile } from "../form-components/file-upload/file-upload";
import { Input } from "../form-components/input/input";
import { PhoneInput } from "../form-components/phone-number/phone-number";
import { RadioGroup } from "../form-components/radio-button/radio-button";
import { TagSelector } from "../form-components/tag-selector/tags-selector";
import { TextArea } from "../form-components/text-area/text-area";
import { InputNumber } from "../form-components/input-number/input-number";
import { Select } from "../form-components/select/select";

export const fieldMap = {
  TEXT: Input,
  TEXTAREA: TextArea,
  MULTI_SELECT: TagSelector,
  NUMBER: Input,
  IMAGE: RDSUploadFile,
  DATE: DatePicker,
  BOOLEAN: RadioGroup,
  EDITOR: RichTextEditor,
  UPLOAD: ButtonFileUpload,
  PHONE_NUMBER: PhoneInput,
  UPLOAD_FILE: UploadFile,
  COLOR_PICKER: ColorPicker,
  COUNTER: InputNumber,
  SELECT: Select,
};
